#!/usr/bin/env python3
"""
Simple HTTP server with CORS support for crypto dashboard
Run with: python3 cors_server.py
"""

import http.server
import socketserver
import json
import urllib.request
import urllib.error
import ssl

class CORSHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, X-API-Key')
        super().end_headers()

    def do_OPTIONS(self):
        self.send_response(200)
        self.end_headers()

    def do_GET(self):
        # Handle API proxy requests
        if self.path.startswith('/api-proxy/'):
            self.handle_api_proxy()
        else:
            # Serve static files normally
            super().do_GET()

    def handle_api_proxy(self):
        try:
            # Extract the real API URL from the path
            api_path = self.path[11:]  # Remove '/api-proxy/'

            # Decode the URL
            import urllib.parse
            real_url = urllib.parse.unquote(api_path)

            print(f"Proxying request to: {real_url}")

            # Make the request to the real API
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }

            # Forward any API key headers
            if 'X-API-Key' in self.headers:
                headers['X-API-Key'] = self.headers['X-API-Key']

            req = urllib.request.Request(real_url, headers=headers)

            # Create SSL context that doesn't verify certificates (for development only)
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE

            with urllib.request.urlopen(req, timeout=10, context=ssl_context) as response:
                data = response.read()
                content_type = response.headers.get('Content-Type', 'application/json')

                # Send successful response
                self.send_response(200)
                self.send_header('Content-Type', content_type)
                self.end_headers()
                self.wfile.write(data)

                print(f"Successfully proxied {len(data)} bytes")

        except urllib.error.HTTPError as e:
            print(f"HTTP Error: {e.code} - {e.reason}")
            try:
                error_data = e.read()
                self.send_response(e.code)
                self.send_header('Content-Type', 'application/json')
                self.end_headers()
                self.wfile.write(error_data)
            except:
                self.send_response(e.code)
                self.send_header('Content-Type', 'application/json')
                self.end_headers()
                error_response = json.dumps({"error": f"HTTP {e.code}: {e.reason}"})
                self.wfile.write(error_response.encode())

        except Exception as e:
            print(f"Proxy error: {str(e)}")
            import traceback
            traceback.print_exc()
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            error_response = json.dumps({"error": str(e)})
            self.wfile.write(error_response.encode())

def run_server(port=8000):
    handler = CORSHTTPRequestHandler
    
    with socketserver.TCPServer(("", port), handler) as httpd:
        print(f"🚀 CORS-enabled server running at http://localhost:{port}")
        print(f"📊 Your crypto dashboard: http://localhost:{port}")
        print(f"🔧 API proxy available at: http://localhost:{port}/api-proxy/")
        print("Press Ctrl+C to stop the server")
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 Server stopped")

if __name__ == "__main__":
    run_server()
