# Crypto Trading Dashboard

A real-time cryptocurrency trading dashboard with advanced signal detection, whale monitoring, and sentiment analysis.

## Features

- 📊 **Real-time data** for top 100 cryptocurrencies (displays top 20)
- 🐋 **Whale detection** using volume analysis and on-chain data
- 📰 **News sentiment analysis** with real-time scoring
- 🚀 **Trading signals** across multiple timeframes
- 📈 **Interactive charts** with volume and price data
- 🔄 **Cross-correlation analysis** between assets

## Quick Start

### Prerequisites
- Python 3.6 or higher
- Internet connection for API access

### Installation

1. **Clone or download** this repository
2. **Navigate to the directory**
3. **Start the dashboard**:
   ```bash
   python3 cors_server.py
   ```
4. **Open your browser** to: `http://localhost:8000`

### API Configuration

The dashboard supports these free APIs:
- **CryptoCompare** - Real-time crypto data
- **NewsAPI** - News sentiment analysis  
- **AlphaVantage** - Technical indicators
- **Moralis** - On-chain whale detection
- **Financial Modeling Prep** - Additional market data

1. Click the **"API Config"** button in the dashboard
2. Enter your API keys (get free keys from the providers above)
3. Click **"Test APIs"** to verify connections
4. Click **"Save"** to store your configuration

## Files

- `index.html` - Main dashboard interface
- `cors_server.py` - Local server with CORS support
- `start_dashboard.sh` - Quick start script (Linux/Mac)
- `README.md` - This documentation

## Features in Detail

### 🎯 Signal Detection
- **Early Momentum**: Volume spikes and price acceleration
- **Sentiment Velocity**: News-based sentiment analysis
- **Whale Signals**: Large transaction and accumulation detection
- **Technical Breakout**: Support/resistance analysis
- **Cross Correlation**: Asset decoupling signals

### 📊 Data Sources
- **Top 100 coins** tracked (excluding stablecoins)
- **Real-time prices** and 24h changes
- **Volume analysis** and acceleration metrics
- **News sentiment** from multiple sources
- **On-chain data** for whale detection

### 🔧 Technical Details
- **CORS-enabled server** for local API access
- **Automatic fallbacks** if APIs are unavailable
- **Real-time updates** every 30 seconds
- **Responsive design** for desktop and mobile

## Troubleshooting

### Common Issues
1. **"CORS Blocked" errors**: Use the included `cors_server.py` instead of opening the HTML file directly
2. **No data showing**: Check your internet connection and API keys
3. **Server won't start**: Ensure Python 3 is installed and port 8000 is available

### Getting API Keys
- **CryptoCompare**: https://min-api.cryptocompare.com/
- **NewsAPI**: https://newsapi.org/
- **AlphaVantage**: https://www.alphavantage.co/
- **Moralis**: https://moralis.io/
- **Financial Modeling Prep**: https://financialmodelingprep.com/

## License

This project is for educational and personal use. Please respect API rate limits and terms of service.

## Support

For issues or questions, check the browser console (F12) for error messages and ensure all API keys are correctly configured.
