<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live Crypto Early Signal Trading Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .signal-card {
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }
        .signal-active {
            border-left-color: #10b981;
            background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);
        }
        .signal-warning {
            border-left-color: #f59e0b;
            background: linear-gradient(135deg, #fffbeb 0%, #fefce8 100%);
        }
        .signal-danger {
            border-left-color: #ef4444;
            background: linear-gradient(135deg, #fef2f2 0%, #fef2f2 100%);
        }
        .confidence-bar {
            height: 4px;
            border-radius: 2px;
            transition: width 0.5s ease;
        }
        .pulse-dot {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        .scrollbar-thin {
            scrollbar-width: thin;
        }
        .scrollbar-thin::-webkit-scrollbar {
            width: 6px;
        }
        .scrollbar-thin::-webkit-scrollbar-track {
            background: #f1f5f9;
        }
        .scrollbar-thin::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <i class="fas fa-chart-line text-blue-600 text-2xl mr-3"></i>
                    <h1 class="text-2xl font-bold text-gray-900">Crypto Early Signal Dashboard</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-green-500 rounded-full pulse-dot mr-2"></div>
                        <span class="text-sm text-gray-600">Live Data</span>
                    </div>
                    <a href="index.html" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700">
                        <i class="fas fa-desktop mr-2"></i>Local Version
                    </a>
                </div>
            </div>
        </div>
    </header>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <!-- GitHub Pages Notice -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div class="flex items-start">
                <i class="fas fa-info-circle text-blue-500 mt-1 mr-3"></i>
                <div>
                    <h3 class="font-medium text-blue-900">GitHub Pages Version</h3>
                    <p class="text-sm text-blue-700 mt-1">
                        This is the hosted version with limited API access due to CORS restrictions. 
                        For full functionality with all APIs, download and run the 
                        <a href="https://github.com/YOUR_USERNAME/crypto-trading-dashboard" class="underline font-medium">local version</a>.
                    </p>
                </div>
            </div>
        </div>

        <!-- Market Overview -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div class="bg-white p-4 rounded-lg shadow">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-600">Bitcoin</p>
                        <p id="btc-price" class="text-2xl font-bold">Loading...</p>
                        <p id="btc-change" class="text-sm">--%</p>
                    </div>
                    <i class="fab fa-bitcoin text-orange-500 text-3xl"></i>
                </div>
            </div>
            <div class="bg-white p-4 rounded-lg shadow">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-600">Ethereum</p>
                        <p id="eth-price" class="text-2xl font-bold">Loading...</p>
                        <p id="eth-change" class="text-sm">--%</p>
                    </div>
                    <i class="fab fa-ethereum text-blue-500 text-3xl"></i>
                </div>
            </div>
            <div class="bg-white p-4 rounded-lg shadow">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-600">Market Status</p>
                        <p class="text-2xl font-bold text-green-600">Live</p>
                        <p class="text-sm text-gray-500">GitHub Hosted</p>
                    </div>
                    <i class="fas fa-globe text-green-500 text-3xl"></i>
                </div>
            </div>
            <div class="bg-white p-4 rounded-lg shadow">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-600">Fear & Greed</p>
                        <p id="fear-greed" class="text-2xl font-bold">Loading...</p>
                        <p id="fear-greed-text" class="text-sm">Loading...</p>
                    </div>
                    <i class="fas fa-thermometer-half text-purple-500 text-3xl"></i>
                </div>
            </div>
        </div>

        <!-- Download Section -->
        <div class="bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg p-6 mb-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-xl font-bold mb-2">Want Full Functionality?</h3>
                    <p class="text-purple-100">Download the complete dashboard with whale detection, sentiment analysis, and all API integrations.</p>
                </div>
                <a href="https://github.com/YOUR_USERNAME/crypto-trading-dashboard/archive/refs/heads/main.zip" 
                   class="bg-white text-purple-600 px-6 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors">
                    <i class="fas fa-download mr-2"></i>Download Dashboard
                </a>
            </div>
        </div>

        <!-- Basic Market Data -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-lg font-bold text-gray-900 mb-4">
                <i class="fas fa-coins text-yellow-500 mr-2"></i>Live Market Data
            </h3>
            <div id="market-data" class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                <!-- Market data will be populated here -->
            </div>
        </div>
    </div>

    <script>
        // GitHub Pages compatible version - uses only CORS-friendly APIs
        
        async function loadMarketData() {
            try {
                // Use CoinGecko (CORS-friendly)
                const response = await fetch('https://api.coingecko.com/api/v3/simple/price?ids=bitcoin,ethereum,cardano,solana,ripple,polkadot&vs_currencies=usd&include_24hr_change=true');
                const data = await response.json();
                
                // Update main cards
                if (data.bitcoin) {
                    document.getElementById('btc-price').textContent = `$${data.bitcoin.usd.toLocaleString()}`;
                    const btcChange = data.bitcoin.usd_24h_change;
                    const btcChangeEl = document.getElementById('btc-change');
                    btcChangeEl.textContent = `${btcChange > 0 ? '+' : ''}${btcChange.toFixed(2)}%`;
                    btcChangeEl.className = btcChange > 0 ? 'text-sm text-green-600' : 'text-sm text-red-600';
                }
                
                if (data.ethereum) {
                    document.getElementById('eth-price').textContent = `$${data.ethereum.usd.toLocaleString()}`;
                    const ethChange = data.ethereum.usd_24h_change;
                    const ethChangeEl = document.getElementById('eth-change');
                    ethChangeEl.textContent = `${ethChange > 0 ? '+' : ''}${ethChange.toFixed(2)}%`;
                    ethChangeEl.className = ethChange > 0 ? 'text-sm text-green-600' : 'text-sm text-red-600';
                }
                
                // Update market grid
                const marketGrid = document.getElementById('market-data');
                const coins = [
                    { id: 'bitcoin', name: 'BTC', data: data.bitcoin },
                    { id: 'ethereum', name: 'ETH', data: data.ethereum },
                    { id: 'cardano', name: 'ADA', data: data.cardano },
                    { id: 'solana', name: 'SOL', data: data.solana },
                    { id: 'ripple', name: 'XRP', data: data.ripple },
                    { id: 'polkadot', name: 'DOT', data: data.polkadot }
                ];
                
                marketGrid.innerHTML = coins.map(coin => {
                    if (!coin.data) return '';
                    const change = coin.data.usd_24h_change;
                    const changeColor = change > 0 ? 'text-green-600' : 'text-red-600';
                    const bgColor = change > 0 ? 'bg-green-50' : 'bg-red-50';
                    
                    return `
                        <div class="text-center p-3 ${bgColor} rounded-lg border">
                            <div class="font-bold text-sm">${coin.name}</div>
                            <div class="text-xs font-medium">$${coin.data.usd.toFixed(2)}</div>
                            <div class="text-xs ${changeColor}">${change > 0 ? '+' : ''}${change.toFixed(1)}%</div>
                        </div>
                    `;
                }).join('');
                
            } catch (error) {
                console.error('Error loading market data:', error);
            }
        }
        
        async function loadFearGreed() {
            try {
                const response = await fetch('https://api.alternative.me/fng/');
                const data = await response.json();
                if (data.data && data.data[0]) {
                    document.getElementById('fear-greed').textContent = data.data[0].value;
                    document.getElementById('fear-greed-text').textContent = data.data[0].value_classification;
                }
            } catch (error) {
                console.error('Error loading fear & greed:', error);
            }
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            loadMarketData();
            loadFearGreed();
            
            // Update every 30 seconds
            setInterval(() => {
                loadMarketData();
                loadFearGreed();
            }, 30000);
        });
    </script>
</body>
</html>
